class VehicleModel {
  final String id;
  final String name;
  final String type; // 'car' or 'bike'
  final String brand;
  final String model;
  final int year;
  final double pricePerDay;
  final String imageUrl;
  final String description;
  final bool isAvailable;
  final List<String> features;
  final DateTime createdAt;
  final DateTime updatedAt;

  VehicleModel({
    required this.id,
    required this.name,
    required this.type,
    required this.brand,
    required this.model,
    required this.year,
    required this.pricePerDay,
    required this.imageUrl,
    required this.description,
    required this.isAvailable,
    required this.features,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert VehicleModel to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'brand': brand,
      'model': model,
      'year': year,
      'pricePerDay': pricePerDay,
      'imageUrl': imageUrl,
      'description': description,
      'isAvailable': isAvailable,
      'features': features,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  // Create VehicleModel from Firestore document
  factory VehicleModel.fromMap(Map<String, dynamic> map) {
    return VehicleModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      type: map['type'] ?? '',
      brand: map['brand'] ?? '',
      model: map['model'] ?? '',
      year: map['year']?.toInt() ?? 0,
      pricePerDay: map['pricePerDay']?.toDouble() ?? 0.0,
      imageUrl: map['imageUrl'] ?? '',
      description: map['description'] ?? '',
      isAvailable: map['isAvailable'] ?? true,
      features: List<String>.from(map['features'] ?? []),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
    );
  }

  // Create a copy of VehicleModel with updated fields
  VehicleModel copyWith({
    String? id,
    String? name,
    String? type,
    String? brand,
    String? model,
    int? year,
    double? pricePerDay,
    String? imageUrl,
    String? description,
    bool? isAvailable,
    List<String>? features,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VehicleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      year: year ?? this.year,
      pricePerDay: pricePerDay ?? this.pricePerDay,
      imageUrl: imageUrl ?? this.imageUrl,
      description: description ?? this.description,
      isAvailable: isAvailable ?? this.isAvailable,
      features: features ?? this.features,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'VehicleModel(id: $id, name: $name, type: $type, brand: $brand, model: $model, year: $year, pricePerDay: $pricePerDay, imageUrl: $imageUrl, description: $description, isAvailable: $isAvailable, features: $features, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VehicleModel && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
