import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:farautorentify/models/user_model.dart';
import 'package:farautorentify/services/auth_service.dart';
import 'package:farautorentify/utils/constants.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _currentUser != null;
  bool get isAdmin => _currentUser?.role == AppConstants.adminRole;
  bool get isCustomer => _currentUser?.role == AppConstants.customerRole;

  // Initialize auth state
  Future<void> initializeAuth() async {
    _setLoading(true);
    try {
      // Check if user is logged in locally
      bool loggedIn = await _authService.isLoggedIn();
      if (loggedIn) {
        // Get current user data
        _currentUser = await _authService.getCurrentUserData();
      }
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Sign up (Customer only)
  Future<bool> signUp({
    required String name,
    required String email,
    required String password,
    required String phone,
    required String cnic,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      UserModel? user = await _authService.signUpWithEmailAndPassword(
        name: name,
        email: email,
        password: password,
        phone: phone,
        cnic: cnic,
      );

      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      }
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    } finally {
      _setLoading(false);
    }
    return false;
  }

  // Sign in
  Future<bool> signIn({
    required String email,
    required String password,
    required String role,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      UserModel? user = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
        role: role,
      );

      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      }
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    } finally {
      _setLoading(false);
    }
    return false;
  }

  // Sign out
  Future<void> signOut() async {
    _setLoading(true);
    try {
      await _authService.signOut();
      _currentUser = null;
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to sign out: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();
    
    try {
      await _authService.resetPassword(email);
      return true;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
    } catch (e) {
      _setError('Failed to reset password: $e');
    } finally {
      _setLoading(false);
    }
    return false;
  }

  // Refresh current user data
  Future<void> refreshUserData() async {
    try {
      if (_currentUser != null) {
        UserModel? updatedUser = await _authService.getCurrentUserData();
        if (updatedUser != null) {
          _currentUser = updatedUser;
          notifyListeners();
        }
      }
    } catch (e) {
      print('Error refreshing user data: $e');
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Get user-friendly error messages
  String _getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed. Please contact support.';
      case 'invalid-admin-credentials':
        return 'Invalid admin credentials. Please check your login details.';
      case 'wrong-role':
        return e.message ?? 'You are not authorized for this role.';
      case 'network-request-failed':
        return 'Network error. Please check your internet connection.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }

  // Clear all data (for testing or logout)
  void clearData() {
    _currentUser = null;
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }

  // Check if current user has admin privileges
  bool hasAdminPrivileges() {
    return _currentUser?.role == AppConstants.adminRole;
  }

  // Check if current user has customer privileges
  bool hasCustomerPrivileges() {
    return _currentUser?.role == AppConstants.customerRole;
  }

  // Get current user ID
  String? getCurrentUserId() {
    return _currentUser?.uid;
  }

  // Get current user role
  String? getCurrentUserRole() {
    return _currentUser?.role;
  }

  // Get current user name
  String? getCurrentUserName() {
    return _currentUser?.name;
  }

  // Get current user email
  String? getCurrentUserEmail() {
    return _currentUser?.email;
  }
}
