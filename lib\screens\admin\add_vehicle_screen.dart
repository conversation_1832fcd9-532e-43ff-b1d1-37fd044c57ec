import 'package:flutter/material.dart';
import 'package:farautorentify/models/vehicle_model.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/utils/validators.dart';
import 'package:farautorentify/widgets/custom_button.dart';
import 'package:farautorentify/widgets/custom_text_field.dart';

class AddVehicleScreen extends StatefulWidget {
  const AddVehicleScreen({super.key});

  @override
  State<AddVehicleScreen> createState() => _AddVehicleScreenState();
}

class _AddVehicleScreenState extends State<AddVehicleScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseService _databaseService = DatabaseService();
  
  // Controllers
  final _nameController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _yearController = TextEditingController();
  final _priceController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  // Dropdown values
  String _selectedType = AppConstants.carType;
  String _selectedCategory = 'Sedan';
  List<String> _selectedFeatures = [];
  bool _isAvailable = true;
  bool _isLoading = false;

  // Categories for each type
  final Map<String, List<String>> _categories = {
    AppConstants.carType: ['Sedan', 'SUV', 'Hatchback', 'Electric'],
    AppConstants.bikeType: ['Sport', 'Cruiser', 'Scooter', 'Electric'],
  };

  @override
  void initState() {
    super.initState();
    _selectedCategory = _categories[_selectedType]!.first;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _yearController.dispose();
    _priceController.dispose();
    _imageUrlController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        title: const Text(
          'Add Vehicle',
          style: TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information Section
              _buildSectionCard(
                title: 'Basic Information',
                children: [
                  NameTextField(
                    controller: _nameController,
                    label: 'Vehicle Name',
                    hint: 'Enter vehicle name',
                    validator: Validators.validateVehicleName,
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  // Type and Category Row
                  Row(
                    children: [
                      Expanded(
                        child: _buildDropdownField(
                          label: 'Type',
                          value: _selectedType,
                          items: [AppConstants.carType, AppConstants.bikeType],
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value!;
                              _selectedCategory = _categories[_selectedType]!.first;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: _buildDropdownField(
                          label: 'Category',
                          value: _selectedCategory,
                          items: _categories[_selectedType]!,
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  NameTextField(
                    controller: _brandController,
                    label: 'Brand',
                    hint: 'Enter brand name',
                    validator: (value) => Validators.validateRequired(value, 'Brand'),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  NameTextField(
                    controller: _modelController,
                    label: 'Model',
                    hint: 'Enter model name',
                    validator: (value) => Validators.validateRequired(value, 'Model'),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.paddingLarge),
              
              // Details Section
              _buildSectionCard(
                title: 'Details',
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          label: 'Year',
                          hint: 'Enter year',
                          controller: _yearController,
                          keyboardType: TextInputType.number,
                          validator: Validators.validateYear,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: CustomTextField(
                          label: 'Price per Day (\$)',
                          hint: 'Enter price',
                          controller: _priceController,
                          keyboardType: TextInputType.number,
                          validator: (value) => Validators.validateNumber(value, 'Price'),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  CustomTextField(
                    label: 'Image URL',
                    hint: 'Enter image URL',
                    controller: _imageUrlController,
                    validator: Validators.validateUrl,
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  CustomTextField(
                    label: 'Description',
                    hint: 'Enter vehicle description',
                    controller: _descriptionController,
                    maxLines: 3,
                    validator: Validators.validateDescription,
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.paddingLarge),
              
              // Features Section
              _buildSectionCard(
                title: 'Features',
                children: [
                  _buildFeaturesSelection(),
                ],
              ),
              
              const SizedBox(height: AppConstants.paddingLarge),
              
              // Availability Section
              _buildSectionCard(
                title: 'Availability',
                children: [
                  SwitchListTile(
                    title: const Text('Available for Booking'),
                    subtitle: Text(
                      _isAvailable 
                          ? 'Vehicle is available for customers to book'
                          : 'Vehicle is not available for booking',
                    ),
                    value: _isAvailable,
                    onChanged: (value) {
                      setState(() {
                        _isAvailable = value;
                      });
                    },
                    activeColor: AppConstants.primaryColor,
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.paddingXLarge),
              
              // Add Button
              PrimaryButton(
                text: 'Add Vehicle',
                width: double.infinity,
                isLoading: _isLoading,
                onPressed: _handleAddVehicle,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ...children,
        ],
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w500,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        DropdownButtonFormField<String>(
          value: value,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
          items: items.map((item) {
            return DropdownMenuItem(
              value: item,
              child: Text(item.toUpperCase()),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildFeaturesSelection() {
    final availableFeatures = _selectedType == AppConstants.carType
        ? AppConstants.carFeatures
        : AppConstants.bikeFeatures;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Features:',
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w500,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Wrap(
          spacing: AppConstants.paddingSmall,
          runSpacing: AppConstants.paddingSmall,
          children: availableFeatures.map((feature) {
            final isSelected = _selectedFeatures.contains(feature);
            return FilterChip(
              label: Text(feature),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedFeatures.add(feature);
                  } else {
                    _selectedFeatures.remove(feature);
                  }
                });
              },
              selectedColor: AppConstants.primaryColor.withOpacity(0.2),
              checkmarkColor: AppConstants.primaryColor,
            );
          }).toList(),
        ),
        if (_selectedFeatures.isEmpty) ...[
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Please select at least one feature',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _handleAddVehicle() async {
    if (!_formKey.currentState!.validate()) return;
    
    if (_selectedFeatures.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one feature'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final vehicle = VehicleModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        type: _selectedType,
        brand: _brandController.text.trim(),
        model: _modelController.text.trim(),
        year: int.parse(_yearController.text),
        pricePerDay: double.parse(_priceController.text),
        imageUrl: _imageUrlController.text.trim(),
        description: _descriptionController.text.trim(),
        isAvailable: _isAvailable,
        features: _selectedFeatures,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.addVehicle(vehicle);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Vehicle added successfully!'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add vehicle: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
