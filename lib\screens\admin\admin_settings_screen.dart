import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/utils/validators.dart';
import 'package:farautorentify/widgets/custom_button.dart';
import 'package:farautorentify/widgets/custom_text_field.dart';

class AdminSettingsScreen extends StatefulWidget {
  const AdminSettingsScreen({super.key});

  @override
  State<AdminSettingsScreen> createState() => _AdminSettingsScreenState();
}

class _AdminSettingsScreenState extends State<AdminSettingsScreen> {
  bool _isDarkMode = false;
  bool _isLoading = false;

  // Password change controllers
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _passwordFormKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _loadThemePreference();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('isDarkMode') ?? false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme Settings Section
            _buildSectionCard(
              title: 'Appearance',
              icon: Icons.palette,
              children: [_buildThemeToggle()],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Security Settings Section
            _buildSectionCard(
              title: 'Security',
              icon: Icons.security,
              children: [_buildPasswordChangeSection()],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // App Information Section
            _buildSectionCard(
              title: 'App Information',
              icon: Icons.info,
              children: [_buildAppInfo()],
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // Danger Zone Section
            _buildSectionCard(
              title: 'Danger Zone',
              icon: Icons.warning,
              children: [_buildDangerZone()],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppConstants.primaryColor, size: 24),
              const SizedBox(width: AppConstants.paddingMedium),
              Text(
                title,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeXLarge,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.textPrimaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ...children,
        ],
      ),
    );
  }

  Widget _buildThemeToggle() {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Dark Mode'),
          subtitle: Text(
            _isDarkMode ? 'Dark theme is enabled' : 'Light theme is enabled',
          ),
          value: _isDarkMode,
          onChanged: _toggleTheme,
          activeColor: AppConstants.primaryColor,
          secondary: Icon(
            _isDarkMode ? Icons.dark_mode : Icons.light_mode,
            color: AppConstants.primaryColor,
          ),
        ),
        const Divider(),
        ListTile(
          leading: Icon(Icons.color_lens, color: AppConstants.primaryColor),
          title: const Text('Theme Color'),
          subtitle: const Text('Primary Blue'),
          trailing: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: AppConstants.primaryColor,
              shape: BoxShape.circle,
            ),
          ),
          onTap: () {
            // TODO: Implement color picker
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Color customization coming soon!'),
                backgroundColor: AppConstants.primaryColor,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPasswordChangeSection() {
    return Form(
      key: _passwordFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Change Password',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          PasswordTextField(
            controller: _currentPasswordController,
            label: 'Current Password',
            hint: 'Enter your current password',
            validator: Validators.validatePassword,
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          PasswordTextField(
            controller: _newPasswordController,
            label: 'New Password',
            hint: 'Enter your new password',
            validator: Validators.validatePassword,
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          PasswordTextField(
            controller: _confirmPasswordController,
            label: 'Confirm New Password',
            hint: 'Confirm your new password',
            validator:
                (value) => Validators.validateConfirmPassword(
                  value,
                  _newPasswordController.text,
                ),
          ),
          const SizedBox(height: AppConstants.paddingLarge),

          PrimaryButton(
            text: 'Change Password',
            width: double.infinity,
            isLoading: _isLoading,
            onPressed: _handlePasswordChange,
          ),
        ],
      ),
    );
  }

  Widget _buildAppInfo() {
    return Column(
      children: [
        _buildInfoRow('App Name', AppConstants.appName),
        _buildInfoRow('Version', AppConstants.appVersion),
        _buildInfoRow('Build', 'Debug'),
        _buildInfoRow('Platform', 'Flutter Web'),
        const SizedBox(height: AppConstants.paddingMedium),

        Row(
          children: [
            Expanded(
              child: SecondaryButton(
                text: 'Privacy Policy',
                onPressed: () {
                  // TODO: Show privacy policy
                  _showComingSoon('Privacy Policy');
                },
              ),
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            Expanded(
              child: SecondaryButton(
                text: 'Terms of Service',
                onPressed: () {
                  // TODO: Show terms of service
                  _showComingSoon('Terms of Service');
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.w500,
                color: AppConstants.textPrimaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDangerZone() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: AppConstants.errorColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(
              AppConstants.borderRadiusMedium,
            ),
            border: Border.all(color: AppConstants.errorColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.warning, color: AppConstants.errorColor, size: 20),
              const SizedBox(width: AppConstants.paddingSmall),
              Expanded(
                child: Text(
                  'Danger Zone - These actions cannot be undone',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.errorColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        DangerButton(
          text: 'Reset All Data',
          width: double.infinity,
          onPressed: () {
            // TODO: Implement data reset
            _showComingSoon('Data Reset');
          },
        ),
      ],
    );
  }

  Future<void> _toggleTheme(bool value) async {
    setState(() {
      _isDarkMode = value;
    });

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', value);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Theme changed to ${value ? 'Dark' : 'Light'} mode'),
          backgroundColor: AppConstants.successColor,
        ),
      );
    }

    // TODO: Implement actual theme switching in the app
    // This would require a theme provider and updating the MaterialApp
  }

  Future<void> _handlePasswordChange() async {
    if (!_passwordFormKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('No user logged in');
      }

      // Re-authenticate user with current password
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: _currentPasswordController.text,
      );

      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(_newPasswordController.text);

      if (mounted) {
        // Clear form
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmPasswordController.clear();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Password changed successfully!'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } on FirebaseAuthException catch (e) {
      if (mounted) {
        String errorMessage;
        switch (e.code) {
          case 'wrong-password':
            errorMessage = 'Current password is incorrect';
            break;
          case 'weak-password':
            errorMessage = 'New password is too weak';
            break;
          case 'requires-recent-login':
            errorMessage =
                'Please log out and log in again before changing password';
            break;
          default:
            errorMessage = 'Failed to change password: ${e.message}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to change password: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature is coming soon!'),
        backgroundColor: AppConstants.primaryColor,
      ),
    );
  }
}
