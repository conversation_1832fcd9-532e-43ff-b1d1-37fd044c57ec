import 'package:flutter/material.dart';
import 'package:farautorentify/screens/auth/login_screen.dart';
import 'package:farautorentify/utils/constants.dart';

class RoleSelectionScreen extends StatelessWidget {
  const RoleSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              const Spacer(),
              // App Logo/Title
              Container(
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor,
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadiusXLarge,
                  ),
                ),
                child: const Icon(
                  Icons.directions_car,
                  size: 80,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: AppConstants.paddingLarge),

              // App Name
              Text(
                AppConstants.appName,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeHeading,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.textPrimaryColor,
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),

              // Subtitle
              Text(
                'Your trusted car and bike rental service',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  color: AppConstants.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.paddingXLarge * 2),

              // Role Selection Title
              Text(
                'Choose Your Role',
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeXXLarge,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.textPrimaryColor,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              Text(
                'Select how you want to access the application',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  color: AppConstants.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.paddingXLarge),

              // Role Cards
              Row(
                children: [
                  Expanded(
                    child: _buildRoleCard(
                      context: context,
                      title: 'Customer',
                      subtitle: 'Book cars and bikes',
                      icon: Icons.person,
                      color: AppConstants.primaryColor,
                      onTap:
                          () => _navigateToLogin(
                            context,
                            AppConstants.customerRole,
                          ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: _buildRoleCard(
                      context: context,
                      title: 'Admin',
                      subtitle: 'Manage vehicles and bookings',
                      icon: Icons.admin_panel_settings,
                      color: AppConstants.secondaryColor,
                      onTap:
                          () =>
                              _navigateToLogin(context, AppConstants.adminRole),
                    ),
                  ),
                ],
              ),

              const Spacer(),

              // Footer
              Text(
                'Version ${AppConstants.appVersion}',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  color: AppConstants.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(
                  AppConstants.borderRadiusLarge,
                ),
              ),
              child: Icon(icon, size: 40, color: color),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              title,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeXLarge,
                fontWeight: FontWeight.w600,
                color: AppConstants.textPrimaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToLogin(BuildContext context, String role) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => LoginScreen(role: role)),
    );
  }
}
