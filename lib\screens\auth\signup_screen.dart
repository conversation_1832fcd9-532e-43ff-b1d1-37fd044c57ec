import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:farautorentify/providers/auth_provider.dart';
import 'package:farautorentify/screens/customer/customer_home_screen.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/utils/validators.dart';
import 'package:farautorentify/widgets/custom_button.dart';
import 'package:farautorentify/widgets/custom_text_field.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _cnicController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();
    _cnicController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimaryColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Create Account',
          style: TextStyle(
            color: AppConstants.textPrimaryColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Header
                _buildHeader(),
                
                const SizedBox(height: AppConstants.paddingXLarge),
                
                // Sign Up Form
                _buildSignUpForm(),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Sign Up Button
                _buildSignUpButton(),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Login Link
                _buildLoginLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Join FarAutoRentify',
          style: TextStyle(
            fontSize: AppConstants.fontSizeHeading,
            fontWeight: FontWeight.bold,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          'Create your account to start booking vehicles.',
          style: TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            color: AppConstants.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpForm() {
    return Column(
      children: [
        NameTextField(
          controller: _nameController,
          validator: Validators.validateName,
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        EmailTextField(
          controller: _emailController,
          validator: Validators.validateEmail,
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        PhoneTextField(
          controller: _phoneController,
          validator: Validators.validatePhone,
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        CNICTextField(
          controller: _cnicController,
          validator: Validators.validateCNIC,
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        PasswordTextField(
          controller: _passwordController,
          validator: Validators.validatePassword,
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        PasswordTextField(
          controller: _confirmPasswordController,
          label: 'Confirm Password',
          hint: 'Re-enter your password',
          validator: (value) => Validators.validateConfirmPassword(
            value,
            _passwordController.text,
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return PrimaryButton(
          text: 'Create Account',
          width: double.infinity,
          isLoading: authProvider.isLoading,
          onPressed: () => _handleSignUp(authProvider),
        );
      },
    );
  }

  Widget _buildLoginLink() {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Already have an account? ',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Sign In',
              style: TextStyle(
                color: AppConstants.primaryColor,
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSignUp(AuthProvider authProvider) async {
    if (!_formKey.currentState!.validate()) return;

    final success = await authProvider.signUp(
      name: _nameController.text.trim(),
      email: _emailController.text.trim(),
      password: _passwordController.text,
      phone: _phoneController.text.trim(),
      cnic: _cnicController.text.trim(),
    );

    if (success && mounted) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(AppConstants.signupSuccess),
          backgroundColor: AppConstants.successColor,
        ),
      );

      // Navigate to customer home screen
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const CustomerHomeScreen(),
        ),
      );
    } else if (authProvider.errorMessage != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authProvider.errorMessage!),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }
}
