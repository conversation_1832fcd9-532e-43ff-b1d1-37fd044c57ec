import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:farautorentify/models/vehicle_model.dart';
import 'package:farautorentify/models/booking_model.dart';
import 'package:farautorentify/providers/auth_provider.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/widgets/custom_button.dart';

class BookingScreen extends StatefulWidget {
  final VehicleModel vehicle;

  const BookingScreen({super.key, required this.vehicle});

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  final _formKey = GlobalKey<FormState>();
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isLoading = false;
  double _totalPrice = 0.0;
  int _numberOfDays = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        title: const Text(
          'Book Vehicle',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Vehicle Summary Card
              _buildVehicleSummary(),

              const SizedBox(height: AppConstants.paddingLarge),

              // Date Selection
              _buildDateSelection(),

              const SizedBox(height: AppConstants.paddingLarge),

              // Price Breakdown
              if (_numberOfDays > 0) _buildPriceBreakdown(),

              const SizedBox(height: AppConstants.paddingLarge),

              // Customer Information
              _buildCustomerInfo(),

              const SizedBox(height: AppConstants.paddingXLarge),

              // Book Button
              _buildBookButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVehicleSummary() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Vehicle Image Placeholder
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusMedium,
              ),
            ),
            child: const Icon(
              Icons.directions_car,
              size: 40,
              color: Colors.grey,
            ),
          ),

          const SizedBox(width: AppConstants.paddingMedium),

          // Vehicle Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.vehicle.name,
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.textPrimaryColor,
                  ),
                ),
                Text(
                  '${widget.vehicle.brand} ${widget.vehicle.model} (${widget.vehicle.year})',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  '\$${widget.vehicle.pricePerDay.toStringAsFixed(0)}/day',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Rental Period',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          // Start Date
          _buildDateField(
            label: 'Start Date',
            date: _startDate,
            onTap: () => _selectDate(context, true),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // End Date
          _buildDateField(
            label: 'End Date',
            date: _endDate,
            onTap: () => _selectDate(context, false),
          ),

          if (_numberOfDays > 0) ...[
            const SizedBox(height: AppConstants.paddingMedium),
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(
                  AppConstants.borderRadiusMedium,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppConstants.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Text(
                    'Rental period: $_numberOfDays day${_numberOfDays > 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: AppConstants.primaryColor,
              size: 20,
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                  Text(
                    date != null
                        ? '${date.day}/${date.month}/${date.year}'
                        : 'Select $label',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      color:
                          date != null
                              ? AppConstants.textPrimaryColor
                              : AppConstants.textSecondaryColor,
                      fontWeight:
                          date != null ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppConstants.textSecondaryColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceBreakdown() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Price Breakdown',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          _buildPriceRow(
            'Daily Rate',
            '\$${widget.vehicle.pricePerDay.toStringAsFixed(0)}',
          ),
          _buildPriceRow('Number of Days', '$_numberOfDays'),
          _buildPriceRow(
            'Subtotal',
            '\$${(_numberOfDays * widget.vehicle.pricePerDay).toStringAsFixed(0)}',
          ),

          const Divider(),

          _buildPriceRow(
            'Total Amount',
            '\$${_totalPrice.toStringAsFixed(0)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize:
                  isTotal
                      ? AppConstants.fontSizeLarge
                      : AppConstants.fontSizeMedium,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color:
                  isTotal
                      ? AppConstants.textPrimaryColor
                      : AppConstants.textSecondaryColor,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize:
                  isTotal
                      ? AppConstants.fontSizeLarge
                      : AppConstants.fontSizeMedium,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color:
                  isTotal
                      ? AppConstants.primaryColor
                      : AppConstants.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        return Container(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Customer Information',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeXLarge,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.textPrimaryColor,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              _buildInfoRow('Name', user?.name ?? ''),
              _buildInfoRow('Email', user?.email ?? ''),
              _buildInfoRow('Phone', user?.phone ?? ''),
              _buildInfoRow('CNIC', user?.cnic ?? ''),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ),
          Text(
            ': $value',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w500,
              color: AppConstants.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookButton() {
    return PrimaryButton(
      text: 'Confirm Booking',
      width: double.infinity,
      isLoading: _isLoading,
      onPressed: _canBook() ? _handleBooking : null,
    );
  }

  bool _canBook() {
    return _startDate != null &&
        _endDate != null &&
        _numberOfDays > 0 &&
        !_isLoading;
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // Reset end date if it's before start date
          if (_endDate != null && _endDate!.isBefore(_startDate!)) {
            _endDate = null;
          }
        } else {
          if (_startDate != null && picked.isAfter(_startDate!)) {
            _endDate = picked;
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('End date must be after start date'),
                backgroundColor: AppConstants.warningColor,
              ),
            );
            return;
          }
        }

        _calculatePrice();
      });
    }
  }

  void _calculatePrice() {
    if (_startDate != null && _endDate != null) {
      _numberOfDays = _endDate!.difference(_startDate!).inDays + 1;
      _totalPrice = _numberOfDays * widget.vehicle.pricePerDay;
    } else {
      _numberOfDays = 0;
      _totalPrice = 0.0;
    }
  }

  Future<void> _handleBooking() async {
    if (!_formKey.currentState!.validate() || !_canBook()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.currentUser!;

      // Create booking model
      final booking = BookingModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: user.uid,
        vehicleId: widget.vehicle.id,
        vehicleName: widget.vehicle.name,
        vehicleType: widget.vehicle.type,
        startDate: _startDate!,
        endDate: _endDate!,
        totalPrice: _totalPrice,
        status: AppConstants.pendingStatus,
        customerName: user.name,
        customerPhone: user.phone,
        customerEmail: user.email,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // TODO: Save booking to database
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(AppConstants.bookingSuccess),
            backgroundColor: AppConstants.successColor,
          ),
        );

        // Navigate back to home
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Booking failed: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
