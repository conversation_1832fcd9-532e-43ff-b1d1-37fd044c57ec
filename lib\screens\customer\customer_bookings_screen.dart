import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:farautorentify/models/booking_model.dart';
import 'package:farautorentify/providers/auth_provider.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/widgets/custom_button.dart';

class CustomerBookingsScreen extends StatefulWidget {
  const CustomerBookingsScreen({super.key});

  @override
  State<CustomerBookingsScreen> createState() => _CustomerBookingsScreenState();
}

class _CustomerBookingsScreenState extends State<CustomerBookingsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  String _selectedStatusFilter = 'All';

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final userId = authProvider.getCurrentUserId();
        
        if (userId == null) {
          return const Center(
            child: Text(
              'Please login to view your bookings',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          );
        }

        return Column(
          children: [
            // Filter Bar
            _buildFilterBar(),
            
            // Bookings List
            Expanded(
              child: _buildBookingsList(userId),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            const Text(
              'Filter: ',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.w500,
              ),
            ),
            ...[
              'All',
              'Pending',
              'Confirmed',
              'Completed',
              'Cancelled',
            ].map((filter) {
              return Padding(
                padding: const EdgeInsets.only(right: AppConstants.paddingSmall),
                child: FilterChip(
                  label: Text(filter),
                  selected: _selectedStatusFilter == filter,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatusFilter = filter;
                    });
                  },
                  selectedColor: AppConstants.primaryColor.withOpacity(0.2),
                  checkmarkColor: AppConstants.primaryColor,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingsList(String userId) {
    return StreamBuilder<List<BookingModel>>(
      stream: _databaseService.getUserBookings(userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppConstants.errorColor,
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Text(
                  'Error loading bookings',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    color: AppConstants.errorColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  'Please try again later',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          );
        }

        final bookings = snapshot.data ?? [];
        final filteredBookings = _filterBookings(bookings);

        if (filteredBookings.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          itemCount: filteredBookings.length,
          itemBuilder: (context, index) {
            return _buildBookingCard(filteredBookings[index]);
          },
        );
      },
    );
  }

  List<BookingModel> _filterBookings(List<BookingModel> bookings) {
    if (_selectedStatusFilter == 'All') {
      return bookings;
    }
    
    return bookings.where((booking) {
      return booking.status.toLowerCase() == _selectedStatusFilter.toLowerCase();
    }).toList();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_online_outlined,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            _selectedStatusFilter != 'All'
                ? 'No ${_selectedStatusFilter.toLowerCase()} bookings found'
                : 'No bookings yet',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            _selectedStatusFilter != 'All'
                ? 'Try changing the filter to see other bookings'
                : 'Start by booking a vehicle from our collection',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookingCard(BookingModel booking) {
    Color statusColor = _getStatusColor(booking.status);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Booking Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        booking.vehicleName,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textPrimaryColor,
                        ),
                      ),
                      Text(
                        booking.vehicleType.toUpperCase(),
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                    border: Border.all(color: statusColor.withOpacity(0.3)),
                  ),
                  child: Text(
                    booking.status.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: AppConstants.fontSizeSmall,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Booking Details
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDetailColumn(
                          'Start Date',
                          '${booking.startDate.day}/${booking.startDate.month}/${booking.startDate.year}',
                        ),
                      ),
                      Expanded(
                        child: _buildDetailColumn(
                          'End Date',
                          '${booking.endDate.day}/${booking.endDate.month}/${booking.endDate.year}',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  Row(
                    children: [
                      Expanded(
                        child: _buildDetailColumn(
                          'Duration',
                          '${booking.numberOfDays} day${booking.numberOfDays > 1 ? 's' : ''}',
                        ),
                      ),
                      Expanded(
                        child: _buildDetailColumn(
                          'Total Amount',
                          '\$${booking.totalPrice.toStringAsFixed(0)}',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Booking ID and Date
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Booking ID: #${booking.id.substring(0, 8)}',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ),
                Text(
                  'Booked: ${booking.createdAt.day}/${booking.createdAt.month}/${booking.createdAt.year}',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
            
            // Action Buttons
            if (booking.status == AppConstants.pendingStatus || booking.status == AppConstants.confirmedStatus) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              Row(
                children: [
                  Expanded(
                    child: SecondaryButton(
                      text: 'View Details',
                      onPressed: () => _showBookingDetails(booking),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: DangerButton(
                      text: 'Cancel',
                      onPressed: () => _confirmCancelBooking(booking),
                    ),
                  ),
                ],
              ),
            ] else ...[
              const SizedBox(height: AppConstants.paddingMedium),
              SizedBox(
                width: double.infinity,
                child: SecondaryButton(
                  text: 'View Details',
                  onPressed: () => _showBookingDetails(booking),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: AppConstants.textSecondaryColor,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimaryColor,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppConstants.warningColor;
      case 'confirmed':
        return AppConstants.primaryColor;
      case 'completed':
        return AppConstants.successColor;
      case 'cancelled':
        return AppConstants.errorColor;
      default:
        return AppConstants.textSecondaryColor;
    }
  }

  void _showBookingDetails(BookingModel booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Booking Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Vehicle', booking.vehicleName),
              _buildDetailRow('Type', booking.vehicleType.toUpperCase()),
              _buildDetailRow('Start Date', '${booking.startDate.day}/${booking.startDate.month}/${booking.startDate.year}'),
              _buildDetailRow('End Date', '${booking.endDate.day}/${booking.endDate.month}/${booking.endDate.year}'),
              _buildDetailRow('Duration', '${booking.numberOfDays} days'),
              _buildDetailRow('Total Price', '\$${booking.totalPrice.toStringAsFixed(2)}'),
              _buildDetailRow('Status', booking.status.toUpperCase()),
              _buildDetailRow('Booking ID', booking.id),
              _buildDetailRow('Booked On', '${booking.createdAt.day}/${booking.createdAt.month}/${booking.createdAt.year}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _confirmCancelBooking(BookingModel booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: Text(
          'Are you sure you want to cancel your booking for ${booking.vehicleName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _cancelBooking(booking);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppConstants.errorColor,
            ),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelBooking(BookingModel booking) async {
    try {
      await _databaseService.cancelBooking(booking.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Booking cancelled successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel booking: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }
}
