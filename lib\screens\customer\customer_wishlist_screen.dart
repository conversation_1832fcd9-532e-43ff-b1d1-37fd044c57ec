import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:farautorentify/models/vehicle_model.dart';
import 'package:farautorentify/providers/auth_provider.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/screens/customer/vehicle_detail_screen.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/widgets/custom_button.dart';

class CustomerWishlistScreen extends StatefulWidget {
  const CustomerWishlistScreen({super.key});

  @override
  State<CustomerWishlistScreen> createState() => _CustomerWishlistScreenState();
}

class _CustomerWishlistScreenState extends State<CustomerWishlistScreen> {
  final DatabaseService _databaseService = DatabaseService();

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final userId = authProvider.getCurrentUserId();

        if (userId == null) {
          return const Center(
            child: Text(
              'Please login to view your wishlist',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          );
        }

        return _buildWishlistContent(userId);
      },
    );
  }

  Widget _buildWishlistContent(String userId) {
    return StreamBuilder<List<String>>(
      stream: _databaseService.getUserWishlist(userId),
      builder: (context, wishlistSnapshot) {
        if (wishlistSnapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppConstants.primaryColor,
              ),
            ),
          );
        }

        if (wishlistSnapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppConstants.errorColor,
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Text(
                  'Error loading wishlist',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    color: AppConstants.errorColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  'Please try again later',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          );
        }

        final wishlistVehicleIds = wishlistSnapshot.data ?? [];

        if (wishlistVehicleIds.isEmpty) {
          return _buildEmptyState();
        }

        // Now get the actual vehicle data
        return StreamBuilder<List<VehicleModel>>(
          stream: _databaseService.getAllVehicles(),
          builder: (context, vehiclesSnapshot) {
            if (vehiclesSnapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppConstants.primaryColor,
                  ),
                ),
              );
            }

            if (vehiclesSnapshot.hasError) {
              return Center(
                child: Text(
                  'Error loading vehicle details',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    color: AppConstants.errorColor,
                  ),
                ),
              );
            }

            final allVehicles = vehiclesSnapshot.data ?? [];
            final wishlistVehicles =
                allVehicles
                    .where((vehicle) => wishlistVehicleIds.contains(vehicle.id))
                    .toList();

            if (wishlistVehicles.isEmpty) {
              return _buildEmptyState();
            }

            return ListView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              itemCount: wishlistVehicles.length,
              itemBuilder: (context, index) {
                return _buildWishlistCard(wishlistVehicles[index], userId);
              },
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          const Text(
            'Your wishlist is empty',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Add vehicles to your wishlist by tapping the heart icon',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWishlistCard(VehicleModel vehicle, String userId) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Vehicle Image
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.borderRadiusLarge),
                topRight: Radius.circular(AppConstants.borderRadiusLarge),
              ),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.directions_car,
                    size: 60,
                    color: Colors.grey,
                  ),
                ),
                Positioned(
                  top: AppConstants.paddingSmall,
                  right: AppConstants.paddingSmall,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          vehicle.isAvailable
                              ? AppConstants.successColor
                              : AppConstants.errorColor,
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadiusSmall,
                      ),
                    ),
                    child: Text(
                      vehicle.isAvailable ? 'Available' : 'Booked',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeSmall,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: AppConstants.paddingSmall,
                  left: AppConstants.paddingSmall,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          vehicle.type == AppConstants.carType
                              ? AppConstants.primaryColor
                              : AppConstants.secondaryColor,
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadiusSmall,
                      ),
                    ),
                    child: Text(
                      vehicle.type.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeSmall,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Vehicle Details
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            vehicle.name,
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeLarge,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.textPrimaryColor,
                            ),
                          ),
                          Text(
                            '${vehicle.brand} ${vehicle.model} (${vehicle.year})',
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                              color: AppConstants.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '\$${vehicle.pricePerDay.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeXLarge,
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                        Text(
                          'per day',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.paddingSmall),

                // Features (first 3)
                if (vehicle.features.isNotEmpty) ...[
                  Wrap(
                    spacing: AppConstants.paddingSmall,
                    runSpacing: 4,
                    children:
                        vehicle.features.take(3).map((feature) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppConstants.paddingSmall,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppConstants.primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(
                                AppConstants.borderRadiusSmall,
                              ),
                            ),
                            child: Text(
                              feature,
                              style: TextStyle(
                                fontSize: AppConstants.fontSizeSmall,
                                color: AppConstants.primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                ],

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: SecondaryButton(
                        text: 'View Details',
                        icon: Icons.visibility,
                        onPressed: () => _navigateToVehicleDetail(vehicle),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Expanded(
                      child: DangerButton(
                        text: 'Remove',
                        icon: Icons.favorite,
                        onPressed:
                            () => _confirmRemoveFromWishlist(vehicle, userId),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToVehicleDetail(VehicleModel vehicle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VehicleDetailScreen(vehicle: vehicle),
      ),
    );
  }

  void _confirmRemoveFromWishlist(VehicleModel vehicle, String userId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Remove from Wishlist'),
            content: Text(
              'Are you sure you want to remove "${vehicle.name}" from your wishlist?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _removeFromWishlist(vehicle, userId);
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppConstants.errorColor,
                ),
                child: const Text('Remove'),
              ),
            ],
          ),
    );
  }

  Future<void> _removeFromWishlist(VehicleModel vehicle, String userId) async {
    try {
      await _databaseService.removeFromWishlist(userId, vehicle.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${vehicle.name} removed from wishlist'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove from wishlist: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }
}
