import 'package:flutter/material.dart';
import 'package:farautorentify/screens/customer/vehicle_detail_screen.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/models/vehicle_model.dart';

class VehicleListScreen extends StatefulWidget {
  final String vehicleType; // 'car' or 'bike'
  final String category; // 'Sedan', 'SUV', etc.

  const VehicleListScreen({
    super.key,
    required this.vehicleType,
    required this.category,
  });

  @override
  State<VehicleListScreen> createState() => _VehicleListScreenState();
}

class _VehicleListScreenState extends State<VehicleListScreen> {
  List<VehicleModel> vehicles = [];
  bool isLoading = true;
  String selectedSortOption = 'Price: Low to High';

  @override
  void initState() {
    super.initState();
    _loadVehicles();
  }

  void _loadVehicles() {
    // TODO: Replace with actual database call
    // For now, using dummy data
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        vehicles = _getDummyVehicles();
        isLoading = false;
      });
    });
  }

  List<VehicleModel> _getDummyVehicles() {
    // Dummy data for demonstration
    return [
      VehicleModel(
        id: '1',
        name: '${widget.category} Model 1',
        type: widget.vehicleType,
        brand: 'Toyota',
        model: 'Camry',
        year: 2023,
        pricePerDay: 50.0,
        imageUrl: AppConstants.defaultCarImage,
        description: 'Comfortable and reliable ${widget.category.toLowerCase()} perfect for your journey.',
        isAvailable: true,
        features: ['AC', 'GPS', 'Bluetooth', 'USB Charging'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      VehicleModel(
        id: '2',
        name: '${widget.category} Model 2',
        type: widget.vehicleType,
        brand: 'Honda',
        model: 'Accord',
        year: 2022,
        pricePerDay: 45.0,
        imageUrl: AppConstants.defaultCarImage,
        description: 'Fuel efficient ${widget.category.toLowerCase()} with modern features.',
        isAvailable: true,
        features: ['AC', 'Automatic', 'Backup Camera', 'Cruise Control'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      VehicleModel(
        id: '3',
        name: '${widget.category} Model 3',
        type: widget.vehicleType,
        brand: 'Nissan',
        model: 'Altima',
        year: 2023,
        pricePerDay: 55.0,
        imageUrl: AppConstants.defaultCarImage,
        description: 'Premium ${widget.category.toLowerCase()} with luxury features.',
        isAvailable: true,
        features: ['AC', 'Leather Seats', 'Sunroof', 'Premium Audio'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        title: Text(
          '${widget.category} ${widget.vehicleType == AppConstants.carType ? 'Cars' : 'Bikes'}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showSortOptions,
          ),
        ],
      ),
      body: isLoading ? _buildLoadingState() : _buildVehicleList(),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
      ),
    );
  }

  Widget _buildVehicleList() {
    if (vehicles.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // Filter/Sort Bar
        _buildFilterBar(),
        
        // Vehicle List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            itemCount: vehicles.length,
            itemBuilder: (context, index) {
              return _buildVehicleCard(vehicles[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.vehicleType == AppConstants.carType 
                ? Icons.directions_car_outlined 
                : Icons.two_wheeler_outlined,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No ${widget.category} ${widget.vehicleType}s available',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Please check back later or try a different category',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey,
            width: 0.2,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '${vehicles.length} ${widget.vehicleType}s found',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w500,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          Text(
            'Sort: $selectedSortOption',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleCard(VehicleModel vehicle) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Vehicle Image
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.borderRadiusLarge),
                topRight: Radius.circular(AppConstants.borderRadiusLarge),
              ),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.directions_car,
                    size: 60,
                    color: Colors.grey,
                  ),
                ),
                Positioned(
                  top: AppConstants.paddingSmall,
                  right: AppConstants.paddingSmall,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: vehicle.isAvailable 
                          ? AppConstants.successColor 
                          : AppConstants.errorColor,
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                    child: Text(
                      vehicle.isAvailable ? 'Available' : 'Booked',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeSmall,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Vehicle Details
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            vehicle.name,
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeLarge,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.textPrimaryColor,
                            ),
                          ),
                          Text(
                            '${vehicle.brand} ${vehicle.model} (${vehicle.year})',
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                              color: AppConstants.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '\$${vehicle.pricePerDay.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeXLarge,
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                        Text(
                          'per day',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.paddingSmall),
                
                // Features
                Wrap(
                  spacing: AppConstants.paddingSmall,
                  runSpacing: 4,
                  children: vehicle.features.take(4).map((feature) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingSmall,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                      ),
                      child: Text(
                        feature,
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Action Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: vehicle.isAvailable 
                        ? () => _navigateToVehicleDetail(vehicle)
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: AppConstants.paddingMedium,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadiusMedium,
                        ),
                      ),
                    ),
                    child: Text(
                      vehicle.isAvailable ? 'View Details' : 'Not Available',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Sort By',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              ...[
                'Price: Low to High',
                'Price: High to Low',
                'Newest First',
                'Most Popular',
              ].map((option) {
                return ListTile(
                  title: Text(option),
                  trailing: selectedSortOption == option 
                      ? const Icon(Icons.check, color: AppConstants.primaryColor)
                      : null,
                  onTap: () {
                    setState(() {
                      selectedSortOption = option;
                      _sortVehicles(option);
                    });
                    Navigator.pop(context);
                  },
                );
              }),
            ],
          ),
        );
      },
    );
  }

  void _sortVehicles(String option) {
    setState(() {
      switch (option) {
        case 'Price: Low to High':
          vehicles.sort((a, b) => a.pricePerDay.compareTo(b.pricePerDay));
          break;
        case 'Price: High to Low':
          vehicles.sort((a, b) => b.pricePerDay.compareTo(a.pricePerDay));
          break;
        case 'Newest First':
          vehicles.sort((a, b) => b.year.compareTo(a.year));
          break;
        case 'Most Popular':
          // TODO: Implement popularity sorting
          break;
      }
    });
  }

  void _navigateToVehicleDetail(VehicleModel vehicle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VehicleDetailScreen(vehicle: vehicle),
      ),
    );
  }
}
