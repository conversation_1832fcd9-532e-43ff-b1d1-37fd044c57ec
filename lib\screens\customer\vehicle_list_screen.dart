import 'package:flutter/material.dart';
import 'package:farautorentify/screens/customer/vehicle_detail_screen.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/models/vehicle_model.dart';

class VehicleListScreen extends StatefulWidget {
  final String vehicleType; // 'car' or 'bike'
  final String category; // 'Sedan', 'SUV', etc.

  const VehicleListScreen({
    super.key,
    required this.vehicleType,
    required this.category,
  });

  @override
  State<VehicleListScreen> createState() => _VehicleListScreenState();
}

class _VehicleListScreenState extends State<VehicleListScreen> {
  final DatabaseService _databaseService = DatabaseService();
  String selectedSortOption = 'Price: Low to High';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        title: Text(
          '${widget.category} ${widget.vehicleType == AppConstants.carType ? 'Cars' : 'Bikes'}',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showSortOptions,
          ),
        ],
      ),
      body: _buildVehicleList(),
    );
  }

  Widget _buildVehicleList() {
    return StreamBuilder<List<VehicleModel>>(
      stream: _databaseService.getVehiclesByType(widget.vehicleType),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppConstants.primaryColor,
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppConstants.errorColor,
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Text(
                  'Error loading vehicles',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    color: AppConstants.errorColor,
                  ),
                ),
              ],
            ),
          );
        }

        final allVehicles = snapshot.data ?? [];
        final filteredVehicles = _filterVehicles(allVehicles);

        if (filteredVehicles.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            // Filter/Sort Bar
            _buildFilterBar(filteredVehicles.length),

            // Vehicle List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                itemCount: filteredVehicles.length,
                itemBuilder: (context, index) {
                  return _buildVehicleCard(filteredVehicles[index]);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.vehicleType == AppConstants.carType
                ? Icons.directions_car_outlined
                : Icons.two_wheeler_outlined,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No ${widget.category} ${widget.vehicleType}s available',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Please check back later or try a different category',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar(int vehicleCount) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Row(
        children: [
          Text(
            '$vehicleCount ${widget.vehicleType}s found',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w500,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          Text(
            'Sort: $selectedSortOption',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  List<VehicleModel> _filterVehicles(List<VehicleModel> vehicles) {
    // Apply category filter if needed
    List<VehicleModel> filtered = List.from(vehicles);

    // Sort vehicles based on selected option
    switch (selectedSortOption) {
      case 'Price: Low to High':
        filtered.sort((a, b) => a.pricePerDay.compareTo(b.pricePerDay));
        break;
      case 'Price: High to Low':
        filtered.sort((a, b) => b.pricePerDay.compareTo(a.pricePerDay));
        break;
      case 'Newest First':
        filtered.sort((a, b) => b.year.compareTo(a.year));
        break;
      case 'Most Popular':
        // Keep original order for now
        break;
    }

    return filtered;
  }

  Widget _buildVehicleCard(VehicleModel vehicle) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Vehicle Image
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.borderRadiusLarge),
                topRight: Radius.circular(AppConstants.borderRadiusLarge),
              ),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.directions_car,
                    size: 60,
                    color: Colors.grey,
                  ),
                ),
                Positioned(
                  top: AppConstants.paddingSmall,
                  right: AppConstants.paddingSmall,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          vehicle.isAvailable
                              ? AppConstants.successColor
                              : AppConstants.errorColor,
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadiusSmall,
                      ),
                    ),
                    child: Text(
                      vehicle.isAvailable ? 'Available' : 'Booked',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeSmall,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Vehicle Details
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            vehicle.name,
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeLarge,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.textPrimaryColor,
                            ),
                          ),
                          Text(
                            '${vehicle.brand} ${vehicle.model} (${vehicle.year})',
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                              color: AppConstants.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '\$${vehicle.pricePerDay.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeXLarge,
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                        Text(
                          'per day',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.paddingSmall),

                // Features
                Wrap(
                  spacing: AppConstants.paddingSmall,
                  runSpacing: 4,
                  children:
                      vehicle.features.take(4).map((feature) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.paddingSmall,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppConstants.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(
                              AppConstants.borderRadiusSmall,
                            ),
                          ),
                          child: Text(
                            feature,
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeSmall,
                              color: AppConstants.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      }).toList(),
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Action Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed:
                        vehicle.isAvailable
                            ? () => _navigateToVehicleDetail(vehicle)
                            : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: AppConstants.paddingMedium,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadiusMedium,
                        ),
                      ),
                    ),
                    child: Text(
                      vehicle.isAvailable ? 'View Details' : 'Not Available',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Sort By',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              ...[
                'Price: Low to High',
                'Price: High to Low',
                'Newest First',
                'Most Popular',
              ].map((option) {
                return ListTile(
                  title: Text(option),
                  trailing:
                      selectedSortOption == option
                          ? const Icon(
                            Icons.check,
                            color: AppConstants.primaryColor,
                          )
                          : null,
                  onTap: () {
                    setState(() {
                      selectedSortOption = option;
                      _sortVehicles(option);
                    });
                    Navigator.pop(context);
                  },
                );
              }),
            ],
          ),
        );
      },
    );
  }

  void _sortVehicles(String option) {
    setState(() {
      selectedSortOption = option;
    });
  }

  void _navigateToVehicleDetail(VehicleModel vehicle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VehicleDetailScreen(vehicle: vehicle),
      ),
    );
  }
}
