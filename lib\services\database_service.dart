import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:farautorentify/models/user_model.dart';
import 'package:farautorentify/models/vehicle_model.dart';
import 'package:farautorentify/models/booking_model.dart';
import 'package:farautorentify/utils/constants.dart';

class DatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // USERS OPERATIONS

  // Get all users (Admin only)
  Stream<List<UserModel>> getAllUsers() {
    return _firestore
        .collection(AppConstants.usersCollection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => UserModel.fromMap(doc.data()))
            .toList());
  }

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      DocumentSnapshot doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromMap(doc.data() as Map<String, dynamic>);
      }
    } catch (e) {
      print('Error getting user: $e');
    }
    return null;
  }

  // Update user
  Future<void> updateUser(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .update(user.toMap());
    } catch (e) {
      print('Error updating user: $e');
      rethrow;
    }
  }

  // Delete user (Admin only)
  Future<void> deleteUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .delete();
    } catch (e) {
      print('Error deleting user: $e');
      rethrow;
    }
  }

  // VEHICLES OPERATIONS

  // Get all vehicles
  Stream<List<VehicleModel>> getAllVehicles() {
    return _firestore
        .collection(AppConstants.vehiclesCollection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => VehicleModel.fromMap(doc.data()))
            .toList());
  }

  // Get vehicles by type
  Stream<List<VehicleModel>> getVehiclesByType(String type) {
    return _firestore
        .collection(AppConstants.vehiclesCollection)
        .where('type', isEqualTo: type)
        .where('isAvailable', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => VehicleModel.fromMap(doc.data()))
            .toList());
  }

  // Get available vehicles
  Stream<List<VehicleModel>> getAvailableVehicles() {
    return _firestore
        .collection(AppConstants.vehiclesCollection)
        .where('isAvailable', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => VehicleModel.fromMap(doc.data()))
            .toList());
  }

  // Get vehicle by ID
  Future<VehicleModel?> getVehicleById(String vehicleId) async {
    try {
      DocumentSnapshot doc = await _firestore
          .collection(AppConstants.vehiclesCollection)
          .doc(vehicleId)
          .get();

      if (doc.exists) {
        return VehicleModel.fromMap(doc.data() as Map<String, dynamic>);
      }
    } catch (e) {
      print('Error getting vehicle: $e');
    }
    return null;
  }

  // Add vehicle (Admin only)
  Future<void> addVehicle(VehicleModel vehicle) async {
    try {
      await _firestore
          .collection(AppConstants.vehiclesCollection)
          .doc(vehicle.id)
          .set(vehicle.toMap());
    } catch (e) {
      print('Error adding vehicle: $e');
      rethrow;
    }
  }

  // Update vehicle (Admin only)
  Future<void> updateVehicle(VehicleModel vehicle) async {
    try {
      await _firestore
          .collection(AppConstants.vehiclesCollection)
          .doc(vehicle.id)
          .update(vehicle.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      print('Error updating vehicle: $e');
      rethrow;
    }
  }

  // Delete vehicle (Admin only)
  Future<void> deleteVehicle(String vehicleId) async {
    try {
      await _firestore
          .collection(AppConstants.vehiclesCollection)
          .doc(vehicleId)
          .delete();
    } catch (e) {
      print('Error deleting vehicle: $e');
      rethrow;
    }
  }

  // BOOKINGS OPERATIONS

  // Get all bookings (Admin only)
  Stream<List<BookingModel>> getAllBookings() {
    return _firestore
        .collection(AppConstants.bookingsCollection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => BookingModel.fromMap(doc.data()))
            .toList());
  }

  // Get user bookings
  Stream<List<BookingModel>> getUserBookings(String userId) {
    return _firestore
        .collection(AppConstants.bookingsCollection)
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => BookingModel.fromMap(doc.data()))
            .toList());
  }

  // Get booking by ID
  Future<BookingModel?> getBookingById(String bookingId) async {
    try {
      DocumentSnapshot doc = await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .get();

      if (doc.exists) {
        return BookingModel.fromMap(doc.data() as Map<String, dynamic>);
      }
    } catch (e) {
      print('Error getting booking: $e');
    }
    return null;
  }

  // Create booking
  Future<void> createBooking(BookingModel booking) async {
    try {
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(booking.id)
          .set(booking.toMap());
    } catch (e) {
      print('Error creating booking: $e');
      rethrow;
    }
  }

  // Update booking
  Future<void> updateBooking(BookingModel booking) async {
    try {
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(booking.id)
          .update(booking.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      print('Error updating booking: $e');
      rethrow;
    }
  }

  // Cancel booking
  Future<void> cancelBooking(String bookingId) async {
    try {
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .update({
            'status': AppConstants.cancelledStatus,
            'updatedAt': DateTime.now().millisecondsSinceEpoch,
          });
    } catch (e) {
      print('Error cancelling booking: $e');
      rethrow;
    }
  }

  // Delete booking (Admin only)
  Future<void> deleteBooking(String bookingId) async {
    try {
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .delete();
    } catch (e) {
      print('Error deleting booking: $e');
      rethrow;
    }
  }

  // WISHLIST OPERATIONS

  // Get user wishlist
  Stream<List<String>> getUserWishlist(String userId) {
    return _firestore
        .collection(AppConstants.wishlistCollection)
        .doc(userId)
        .snapshots()
        .map((snapshot) {
      if (snapshot.exists) {
        final data = snapshot.data() as Map<String, dynamic>;
        return List<String>.from(data['vehicleIds'] ?? []);
      }
      return <String>[];
    });
  }

  // Add to wishlist
  Future<void> addToWishlist(String userId, String vehicleId) async {
    try {
      await _firestore
          .collection(AppConstants.wishlistCollection)
          .doc(userId)
          .set({
        'vehicleIds': FieldValue.arrayUnion([vehicleId]),
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      }, SetOptions(merge: true));
    } catch (e) {
      print('Error adding to wishlist: $e');
      rethrow;
    }
  }

  // Remove from wishlist
  Future<void> removeFromWishlist(String userId, String vehicleId) async {
    try {
      await _firestore
          .collection(AppConstants.wishlistCollection)
          .doc(userId)
          .update({
        'vehicleIds': FieldValue.arrayRemove([vehicleId]),
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      print('Error removing from wishlist: $e');
      rethrow;
    }
  }

  // Check if vehicle is in wishlist
  Future<bool> isInWishlist(String userId, String vehicleId) async {
    try {
      DocumentSnapshot doc = await _firestore
          .collection(AppConstants.wishlistCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        final vehicleIds = List<String>.from(data['vehicleIds'] ?? []);
        return vehicleIds.contains(vehicleId);
      }
    } catch (e) {
      print('Error checking wishlist: $e');
    }
    return false;
  }
}
